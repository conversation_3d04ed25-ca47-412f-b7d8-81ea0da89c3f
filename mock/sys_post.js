import Mock from 'mockjs'

const mock = {
  'list|10': [
    {
      'roleId|+1': 1,
      'roleName|1': ['系统管理员', '福州系统运维', '产品管理', '前端开发', '后端开发'],
      roleKey: 'admin',
      'roleSort|+1': 0,
      'status|0-1': 0,
      createAt: `${Mock.Random.date()} ${Mock.Random.time()}`,
    },
  ],
}

// 菜单数据
const menuData = [
  {
    menuId: 100,
    menuName: 'admin',
    title: '系统管理',
    icon: 'setting',
    path: '/admin',
    paths: '/0/100',
    menuType: 'M',
    action: '无',
    permission: 'admin:view',
    parentId: 0,
    noCache: false,
    breadcrumb: '',
    component: 'Layout',
    sort: 10,
    visible: '0',
    isFrame: '1',
    sysApi: null,
    apis: null,
    dataScope: '',
    params: '',
    RoleId: 0,
    children: [
      {
        menuId: 101,
        menuName: 'user',
        title: '用户管理',
        icon: 'user',
        path: '/admin/user',
        paths: '/0/100/101',
        menuType: 'C',
        action: '无',
        permission: 'admin:user:list',
        parentId: 100,
        noCache: false,
        breadcrumb: '',
        component: '/admin/user',
        sort: 1,
        visible: '0',
        isFrame: '',
        sysApi: null,
        apis: null,
        dataScope: '',
        params: '',
        RoleId: 0,
        is_select: false,
        createBy: 1,
        updateBy: 1,
        createdAt: '2024-01-01T00:00:00+08:00',
        updatedAt: '2024-01-01T00:00:00+08:00',
      },
      {
        menuId: 102,
        menuName: 'menu',
        title: '菜单管理',
        icon: 'menu',
        path: '/admin/menu',
        paths: '/0/100/102',
        menuType: 'C',
        action: '无',
        permission: 'admin:menu:list',
        parentId: 100,
        noCache: false,
        breadcrumb: '',
        component: '/admin/menu',
        sort: 2,
        visible: '0',
        isFrame: '',
        sysApi: null,
        apis: null,
        dataScope: '',
        params: '',
        RoleId: 0,
        is_select: false,
        createBy: 1,
        updateBy: 1,
        createdAt: '2024-01-01T00:00:00+08:00',
        updatedAt: '2024-01-01T00:00:00+08:00',
      },
      {
        menuId: 103,
        menuName: 'sysApi',
        title: 'API管理',
        icon: 'api',
        path: '/admin/sys-api',
        paths: '/0/100/103',
        menuType: 'C',
        action: '无',
        permission: 'admin:sysApi:list',
        parentId: 100,
        noCache: false,
        breadcrumb: '',
        component: '/admin/sys-api/index',
        sort: 3,
        visible: '0',
        isFrame: '',
        sysApi: null,
        apis: null,
        dataScope: '',
        params: '',
        RoleId: 0,
        is_select: false,
        createBy: 1,
        updateBy: 1,
        createdAt: '2024-01-01T00:00:00+08:00',
        updatedAt: '2024-01-01T00:00:00+08:00',
      },
    ],
    is_select: false,
    createBy: 1,
    updateBy: 1,
    createdAt: '2024-01-01T00:00:00+08:00',
    updatedAt: '2024-01-01T00:00:00+08:00',
  },
  {
    menuId: 200,
    menuName: 'nas',
    title: 'NAS云盘',
    icon: 'cloud-server',
    path: '/nas',
    paths: '/0/200',
    menuType: 'C',
    action: '无',
    permission: 'nas:view',
    parentId: 0,
    noCache: false,
    breadcrumb: '',
    component: '/nas/index',
    sort: 20,
    visible: '0',
    isFrame: '1',
    sysApi: null,
    apis: null,
    dataScope: '',
    params: '',
    RoleId: 0,
    is_select: false,
    createBy: 1,
    updateBy: 1,
    createdAt: '2024-01-01T00:00:00+08:00',
    updatedAt: '2024-01-01T00:00:00+08:00',
  },
  {
    menuId: 300,
    menuName: 'xunlei',
    title: '迅雷业务',
    icon: 'thunderbolt',
    path: '/xunlei',
    paths: '/0/300',
    menuType: 'M',
    action: '无',
    permission: 'xunlei:view',
    parentId: 0,
    noCache: false,
    breadcrumb: '',
    component: 'Layout',
    sort: 30,
    visible: '0',
    isFrame: '1',
    sysApi: null,
    apis: null,
    dataScope: '',
    params: '',
    RoleId: 0,
    children: [
      {
        menuId: 310,
        menuName: 'complaint',
        title: '投诉处理',
        icon: 'exclamation-circle',
        path: '/xunlei/complaint',
        paths: '/0/300/310',
        menuType: 'M',
        action: '无',
        permission: 'xunlei:complaint:view',
        parentId: 300,
        noCache: false,
        breadcrumb: '',
        component: 'Layout',
        sort: 1,
        visible: '0',
        isFrame: '',
        sysApi: null,
        apis: null,
        dataScope: '',
        params: '',
        RoleId: 0,
        children: [
          {
            menuId: 311,
            menuName: 'cloudQuery',
            title: '云盘查询',
            icon: 'search',
            path: '/xunlei/complaint/cloud-query',
            paths: '/0/300/310/311',
            menuType: 'C',
            action: '无',
            permission: 'xunlei:complaint:cloudQuery:view',
            parentId: 310,
            noCache: false,
            breadcrumb: '',
            component: '/xunlei/complaint/cloud-query',
            sort: 1,
            visible: '0',
            isFrame: '',
            sysApi: null,
            apis: null,
            dataScope: '',
            params: '',
            RoleId: 0,
            is_select: false,
            createBy: 1,
            updateBy: 1,
            createdAt: '2024-01-01T00:00:00+08:00',
            updatedAt: '2024-01-01T00:00:00+08:00',
          },
          {
            menuId: 312,
            menuName: 'fileQuery',
            title: '文件查询',
            icon: 'file-search',
            path: '/xunlei/complaint/file-query',
            paths: '/0/300/310/312',
            menuType: 'C',
            action: '无',
            permission: 'xunlei:complaint:fileQuery:view',
            parentId: 310,
            noCache: false,
            breadcrumb: '',
            component: '/xunlei/complaint/file-query',
            sort: 2,
            visible: '0',
            isFrame: '',
            sysApi: null,
            apis: null,
            dataScope: '',
            params: '',
            RoleId: 0,
            is_select: false,
            createBy: 1,
            updateBy: 1,
            createdAt: '2024-01-01T00:00:00+08:00',
            updatedAt: '2024-01-01T00:00:00+08:00',
          },
          {
            menuId: 313,
            menuName: 'userStatus',
            title: '用户状态',
            icon: 'user-check',
            path: '/xunlei/complaint/user-status',
            paths: '/0/300/310/313',
            menuType: 'C',
            action: '无',
            permission: 'xunlei:complaint:userStatus:view',
            parentId: 310,
            noCache: false,
            breadcrumb: '',
            component: '/xunlei/complaint/user-status',
            sort: 3,
            visible: '0',
            isFrame: '',
            sysApi: null,
            apis: null,
            dataScope: '',
            params: '',
            RoleId: 0,
            is_select: false,
            createBy: 1,
            updateBy: 1,
            createdAt: '2024-01-01T00:00:00+08:00',
            updatedAt: '2024-01-01T00:00:00+08:00',
          },
        ],
        is_select: false,
        createBy: 1,
        updateBy: 1,
        createdAt: '2024-01-01T00:00:00+08:00',
        updatedAt: '2024-01-01T00:00:00+08:00',
      },
    ],
    is_select: false,
    createBy: 1,
    updateBy: 1,
    createdAt: '2024-01-01T00:00:00+08:00',
    updatedAt: '2024-01-01T00:00:00+08:00',
  },
]
export const mockLoginData = () => Mock.toJSONSchema(mock).template

export const login = {
  url: '/api/admin/post',
  method: 'get',
  timeout: 500,
  statusCode: 200,
  response: {
    code: 200,
    message: '登陆成功',
    data: Mock.toJSONSchema(mock).template,
  },
}

// 获取菜单接口
export const getUserMenuRole = {
  url: '/mock/api/v1/menurole',
  method: 'get',
  timeout: 500,
  statusCode: 200,
  response: {
    code: 200,
    message: '获取菜单成功',
    data: menuData,
  },
}
