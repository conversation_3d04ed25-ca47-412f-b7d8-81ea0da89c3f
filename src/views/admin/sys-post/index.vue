<template>
  <div class="container">
    <a-card :bordered="false" class="cardStyle" style="margin-bottom: 16px">
      <a-list-item-meta>
        <template #title>
          <div class="akaInfoTitle">岗位管理</div>
        </template>
        <template #description>
          <div class="akaInfoDesc">在这里管理用户所绑定的岗位关系</div>
        </template>
        <template #avatar>
          <div style="border-radius: 100px 0 100px 100px; background-color: #eff4f9; padding: 6px">
            <Iconify
              icon="material-symbols:post-add-sharp"
              style="color: black"
              width="48"
              height="48"
            />
          </div>
        </template>
      </a-list-item-meta>
      <a-divider />
      <a-card-meta>
        <template #avatar>
          <a-form :model="queryForm" ref="queryFormRef" layout="inline">
            <a-form-item name="postCode" label="岗位编码">
              <a-input
                v-model:value="queryForm.postCode"
                placeholder="请输入岗位编码"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="postName" label="岗位名称">
              <a-input
                v-model:value="queryForm.postName"
                placeholder="请输入岗位名称"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="status" label="岗位状态">
              <a-select
                v-model:value="queryForm.status"
                placeholder="请选择岗位状态"
                :style="{ width: '181px' }"
              >
                <a-option :value="2">正常</a-option>
                <a-option :value="1">停用</a-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button v-has="'admin:sysPost:query'" type="primary" @click="handleQuery"
                  ><SearchOutlined /> 搜索</a-button
                >
                <a-button @click="handleResetQuery"><ReloadOutlined /> 重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </template>
      </a-card-meta>
      <template #actions>
        <a-space class="action">
          <a-button v-has="'admin:sysPost:add'" type="primary" @click="handleAdd"
            ><icon-plus /> 新增
          </a-button>
          <a-button
            v-has="'admin:sysPost:remove'"
            type="primary"
            status="danger"
            @click="
              () => {
                deleteVisible = true
              }
            "
            ><icon-delete /> 批量删除
          </a-button>
          <a-button type="primary" status="warning" disabled><icon-download /> 导出 </a-button>
        </a-space>
      </template>
    </a-card>

    <a-card :bordered="false" class="cardStyle">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="{
          'show-total': true,
          'show-jumper': true,
          'show-page-size': true,
          total: pager.count,
          current: currentPage,
        }"
        :row-selection="{ type: 'checkbox' }"
        row-key="postId"
        @selection-change="
          (selection) => {
            deleteData = selection
          }
        "
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #createdAt="{ record }">
          {{ parseTime(record.createdAt) }}
        </template>
        <template #status="{ record }">
          <a-tag v-if="record.status == 2" color="green">正常</a-tag>
          <a-tag v-else color="red">停用</a-tag>
        </template>
        <template #action="{ record }">
          <a-space>
            <a-button v-has="'admin:sysPost:edit'" type="text" @click="handleUpdate(record)"
              ><icon-edit /> 修改</a-button
            >
            <a-button
              v-has="'admin:sysPost:remove'"
              type="text"
              @click="
                () => {
                  deleteVisible = true
                  deleteData = [record.postId]
                }
              "
              ><icon-delete /> 删除</a-button
            >
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- Modal -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      title-align="start"
      @before-ok="handleSubmit"
      @close="
        () => {
          modalFormRef.resetFields()
          modalForm.postId = null
        }
      "
    >
      <a-form :model="modalForm" :rules="rules" ref="modalFormRef">
        <a-form-item name="postName" label="岗位名称">
          <a-input v-model:value="modalForm.postName" placeholder="请输入岗位名称" />
        </a-form-item>
        <a-form-item name="postCode" label="岗位编码">
          <a-input v-model:value="modalForm.postCode" placeholder="请输入岗位编码" />
        </a-form-item>
        <a-form-item name="sort" label="岗位排序">
          <a-input-number
            v-model:value="modalForm.sort"
            :default-value="0"
            :style="{ width: '150px' }"
          />
        </a-form-item>
        <a-form-item name="status" label="岗位状态">
          <a-radio-group v-model:value="modalForm.status">
            <a-radio :value="2"> 正常 </a-radio>
            <a-radio :value="1"> 停用 </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="remark" label="备注">
          <a-textarea v-model:value="modalForm.remark" placeholder="请输入备注内容" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Akiraka 20230223 删除与批量删除 开始 -->
    <DeleteModal
      :data="deleteData"
      :visible="deleteVisible"
      :apiDelete="removePost"
      @deleteVisibleChange="() => (deleteVisible = false)"
    />
    <!-- Akiraka 20230223 删除与批量删除 结束 -->
  </div>
</template>

<script setup lang="ts">
import { getPost, addPost, removePost, updatePost } from '@/api/admin/post'
import { parseTime } from '@/utils/parseTime'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'

// Akiraka 20230210 删除数据
const deleteData = ref([])
// Akiraka 20230210 删除对话框
const deleteVisible = ref(false)
// Akiraka 20230210 监听删除事件
watch(
  () => deleteVisible.value,
  (value) => {
    if (value === false) {
      getPostInfo(pager)
    }
  },
)

// 表单引用
const queryFormRef = ref<FormInstance>()
const modalFormRef = ref<FormInstance>()

const currentPage = ref(1)
// Pager
const pager = {
  count: 0,
  pageIndex: 1,
  pageSize: 10,
}
// form
const queryForm = reactive({})
const modalForm = reactive<any>({
  sort: 0,
  status: 2,
})

// Rules
const rules = {
  postName: [{ required: true, message: '请输入岗位名称' }],
  postCode: [{ required: true, message: '请输入岗位编码' }],
  sort: [{ required: true, message: '请选择岗位排序' }],
}

// Modal
const modalVisible = ref(false)
const modalTitle = ref('默认标题')

// Batch Del List
const batchList = []

// Table Columns
const columns = [
  { title: '岗位编号', dataIndex: 'postId' },
  { title: '岗位编码', dataIndex: 'postCode' },
  { title: '岗位名称', dataIndex: 'postName' },
  { title: '岗位排序', dataIndex: 'sort' },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: '创建时间', dataIndex: 'createdAt', slotName: 'createdAt' },
  { title: '操作', slotName: 'action' },
]

// Table Data
const tableData = ref([])

// 新增
const handleAdd = () => {
  modalVisible.value = true
  modalTitle.value = '新增岗位'
}

// 修改
const handleUpdate = async (record) => {
  modalVisible.value = true
  modalTitle.value = '修改岗位'

  await nextTick()
  Object.assign(modalForm, record)
}

// Modal ok
// 异步关闭Modal需要调用 done()
const handleSubmit = (done) => {
  modalFormRef.value
    ?.validate()
    .then(async () => {
      try {
        if (!modalForm.postId) {
          const response = await addPost(modalForm)
          if (response.status === 200) {
            notification.success({
              message: '新增成功',
              description: '岗位已创建',
            })
          }
        } else {
          const response = await updatePost(modalForm, modalForm.postId)
          if (response.status === 200) {
            notification.success({
              message: '更新成功',
              description: '岗位已更新',
            })
          }
        }
        done()
        getPostInfo(pager)
      } catch (error) {
        notification.error({
          message: '操作失败',
          description: '请稍后重试',
        })
        done(false)
      }
    })
    .catch(() => {
      message.error('表单校验失败')
      done(false)
    })
}

// 批量删除
const handleBatchDelete = () => {
  if (batchList.length !== 0) {
    Modal.warning({
      title: '提示',
      content: '是否批量删除以下选中的数据？',
      onOk: async () => {
        try {
          const response = await removePost({ ids: batchList })
          if (response.status === 200) {
            message.success('批量删除成功')
            getPostInfo(pager)
          }
        } catch (error) {
          message.error('删除失败')
        }
      },
      onCancel: () => {
        message.info('已取消批量删除数据')
      },
    })
  } else {
    message.error('请勾选需要删除的数据！')
  }
}

/**
 * 分页改变
 * @param {Number} [page]
 */
const handlePageChange = (page) => {
  pager.pageIndex = page

  // 修改当前页码
  currentPage.value = page
  getPostInfo({ ...pager, ...queryForm })
}

// 每页数据量
const handlePageSizeChange = (pageSize) => {
  pager.pageSize = pageSize
  getPostInfo({ ...pager, ...queryForm })
}

// 获取岗位信息
const getPostInfo = async (params = {}) => {
  try {
    const response = await getPost(params)
    if (response.status === 200 && response.data) {
      tableData.value = response.data.list || []
      Object.assign(pager, {
        count: response.data.count || 0,
        pageIndex: response.data.pageIndex || 1,
        pageSize: response.data.pageSize || 10,
      })
    }
  } catch (error) {
    notification.error({
      message: '获取数据失败',
      description: '请稍后重试',
    })
  }
}

// 查询岗位信息
const handleQuery = async () => {
  const params = {
    pageIndex: pager.pageIndex,
    pageSize: pager.pageSize,
    ...queryForm,
  }

  getPostInfo(params)
}

// 重置搜索
const handleResetQuery = () => {
  queryFormRef.value?.resetFields()
  getPostInfo(queryForm)
}

onMounted(() => {
  getPostInfo(pager)
})
</script>

<style lang="scss">
.action {
  margin-bottom: 12px;
}
</style>
