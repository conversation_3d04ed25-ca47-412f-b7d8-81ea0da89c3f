<template>
  <div class="app-container">
    <a-form :model="queryForm" ref="queryFormRef" layout="inline">
      <a-form-item name="status" label="状态">
        <a-select v-model:value="queryForm.status" placeholder="请选择系统操作日志状态">
          <a-select-option :value="2">正常</a-select-option>
          <a-select-option :value="1">关闭</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item name="createdAt" label="创建时间">
        <a-range-picker disabled v-model:value="queryForm.createdAt" style="width: 254px" />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" @click="handleQuery">搜索</a-button>
          <a-button @click="handleResetQuery">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <a-divider />

    <div class="action">
      <a-space>
        <a-button
          type="primary"
          status="danger"
          @click="handleBatchDelete"
          :disabled="selectedKeys.length === 0"
        >
          批量删除</a-button
        >
      </a-space>
    </div>

    <a-table
      :data-source="tableData"
      :columns="columns"
      :row-selection="{ type: 'checkbox' }"
      row-key="id"
      :pagination="{
        showTotal: (total: number) => `共 ${total} 条记录`,
        showQuickJumper: true,
        showSizeChanger: true,
        current: currentPage,
        total: pager.count,
      }"
      v-model:selectedRowKeys="selectedKeys"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag v-if="record.status == 2" color="green">正常</a-tag>
          <a-tag v-if="record.status == 1" color="red">关闭</a-tag>
        </template>
        <template v-if="column.key === 'operTime'">
          {{ parseTime(record.operTime) }}
        </template>
        <template v-if="column.key === 'action'">
          <a-popconfirm title="是否删除当前数据？" @confirm="removeSysOperaLogInfo([record.id])">
            <a-button type="text" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getSysOperaLog, removeSysOperaLog } from '@/api/admin/sys-opera-log'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { parseTime } from '@/utils/parseTime'

// 表单引用
const queryFormRef = ref<FormInstance>()

// 当前页
const currentPage = ref(1)

// 分页
const pager = {
  count: 0,
  pageIndex: 1,
  pageSize: 10,
}

//
const selectedKeys = ref([])

const queryForm = reactive({})

const tableData = ref([])
const columns = [
  { title: '编号', dataIndex: 'id' },
  { title: '请求信息', dataIndex: 'operUrl', width: 500 },
  { title: '操作人员', dataIndex: 'operName' },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: '操作日期', dataIndex: 'operTime', slotName: 'operTime' },
  { title: '操作', slotName: 'action' },
]

// 查询
const handleQuery = () => {
  handlePageChange(1)
}

// 重置查询
const handleResetQuery = () => {
  queryFormRef.value?.resetFields()
  handlePageChange(1)
}

// 批量删除
const handleBatchDelete = () => {
  console.log(selectedKeys.value)
  if (selectedKeys.value.length === 0) {
    message.error('请勾选需要删除的数据！')
    return
  }

  Modal.confirm({
    title: '注意',
    content: '是否批量删除选中？',
    onOk: () => {
      removeSysOperaLogInfo(selectedKeys.value)
    },
    onCancel: () => {
      message.info('已取消操作')
    },
  })
}

// 表格变化处理（分页、排序、筛选）
const handleTableChange = (pagination, filters, sorter) => {
  if (pagination) {
    pager.pageIndex = pagination.current
    pager.pageSize = pagination.pageSize
    currentPage.value = pagination.current
  }
  getSysOperaLogInfo({ ...pager, ...queryForm })
}

// 获取操作日志
const getSysOperaLogInfo = async (params = {}) => {
  try {
    const response = await getSysOperaLog(params)
    if (response.status === 200 && response.data) {
      const { count, list, pageIndex, pageSize } = response.data
      tableData.value = list || []
      Object.assign(pager, {
        total: count || 0,
        pageIndex: pageIndex || 1,
        pageSize: pageSize || 10,
      })
    }
  } catch (error) {
    message.error('获取操作日志失败')
  }
}

/**
 * 删除操作日志
 * @params {array} ids
 */
const removeSysOperaLogInfo = async (ids) => {
  try {
    const response = await removeSysOperaLog({ ids })
    if (response.status === 200) {
      message.success('删除成功')
      getSysOperaLogInfo()
    }
  } catch (error) {
    message.error('删除失败')
  }
}

onMounted(() => {
  getSysOperaLogInfo()
})
</script>

<style lang="scss">
.action {
  margin-bottom: 8px;
}
</style>
