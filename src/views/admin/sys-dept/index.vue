<template>
  <div class="container">
    <a-card :bordered="false" class="cardStyle" style="margin-bottom: 16px">
      <a-list-item-meta>
        <template #title>
          <div class="akaInfoTitle">部门管理</div>
        </template>
        <template #description>
          <div class="akaInfoDesc">在这里管理用户所绑定的部门关系</div>
        </template>
        <template #avatar>
          <div style="border-radius: 100px 0 100px 100px; background-color: #eff4f9; padding: 6px">
            <Iconify icon="uil:window-section" style="color: black" width="48" height="48" />
          </div>
        </template>
      </a-list-item-meta>
      <a-divider />
      <a-card-meta>
        <template #avatar>
          <a-form :model="queryForm" ref="queryFormRef" layout="inline">
            <a-form-item name="deptName" label="部门名称">
              <a-input
                v-model:value="queryForm.deptName"
                placeholder="请输入部门名称"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="status" label="部门状态">
              <a-select v-model:value="queryForm.status" placeholder="请选择部门状态">
                <a-option :value="2">正常</a-option>
                <a-option :value="1">停用</a-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button v-has="'admin:sysDept:query'" type="primary" @click="handleQuery"
                  ><SearchOutlined /> 搜索</a-button
                >
                <a-button @click="handleResetQuery"><ReloadOutlined /> 重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </template>
      </a-card-meta>
      <template #actions>
        <a-space class="action">
          <a-button v-has="'admin:sysDept:add'" type="primary" @click="handleAdd()"
            ><PlusOutlined /> 新增</a-button
          >
        </a-space>
      </template>
    </a-card>

    <!-- 异步数据需要defualt-expanded-keys 传入所有行Key才能默认展开 -->
    <a-card :bordered="false" class="cardStyle">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        :default-expanded-keys="[1]"
        row-key="deptId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag color="success" v-if="record.status === 2">正常</a-tag>
            <a-tag color="error" v-else> 停用 </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'createdAt'">
            {{ parseTime(record.createdAt) }}
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button v-has="'admin:sysDept:edit'" type="link" @click="handleUpdate(record)"
              ><EditOutlined /> 修改</a-button
            >
            <a-button v-has="'admin:sysDept:add'" type="link" @click="handleAdd(record)"
              ><PlusOutlined /> 新增</a-button
            >
            <a-button
              v-has="'admin:sysDept:remove'"
              type="link"
              danger
              @click="
                () => {
                  deleteVisible = true
                  deleteData = [record.deptId]
                }
              "
              ><DeleteOutlined /> 删除</a-button
            >
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- Modal -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleBeforeOk"
      @cancel="handleCancel"
    >
      <a-form
        :model="modalForm"
        :rules="rules"
        ref="modalFormRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item name="parentId" label="上级部门">
          <a-tree-select
            v-model:value="modalForm.parentId"
            :tree-data="tableData"
            placeholder="请选择上级部门"
            :default-expand-all="true"
            :fieldNames="{ children: 'children', label: 'deptName', value: 'deptId' }"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item name="deptName" label="部门名称">
          <a-input v-model:value="modalForm.deptName" placeholder="请输入部门名称" />
        </a-form-item>
        <a-form-item name="leader" label="负责人">
          <a-input v-model:value="modalForm.leader" placeholder="请输入负责人" />
        </a-form-item>
        <a-form-item name="phone" label="联系电话">
          <a-input v-model:value="modalForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item name="email" label="邮箱">
          <a-input v-model:value="modalForm.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item name="status" label="状态">
          <a-radio-group v-model:value="modalForm.status">
            <a-radio :value="2">正常</a-radio>
            <a-radio :value="1">停用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Akiraka 20230223 删除与批量删除 开始 -->
    <DeleteModal
      :data="deleteData"
      :visible="deleteVisible"
      :apiDelete="removeDept"
      @deleteVisibleChange="() => (deleteVisible = false)"
    />
    <!-- Akiraka 20230223 删除与批量删除 结束 -->
  </div>
</template>

<script setup lang="ts">
import { getDept, addDept, updateDept, removeDept } from '@/api/admin/dept'

import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { parseTime } from '@/utils/parseTime'

interface DeptForm {
  deptId?: number | string
  parentId?: number | string
  deptName?: string
  leader?: string
  phone?: string
  email?: string
  status?: number
  [key: string]: unknown
}

// 表单引用
const queryFormRef = ref<FormInstance>()
const modalFormRef = ref<FormInstance>()

// Akiraka 20230210 删除数据
const deleteData = ref([])
// Akiraka 20230210 删除对话框
const deleteVisible = ref(false)
// Akiraka 20230210 监听删除事件
watch(
  () => deleteVisible.value,
  (value) => {
    if (value === false) {
      getDeptInfo()
    }
  },
)

// Modal
const modalVisible = ref(false)
const modalTitle = ref('默认标题')

// Form
const queryForm = reactive<DeptForm>({})
const modalForm = reactive<DeptForm>({
  status: 2,
})

// rules
const rules = {
  parentId: [{ required: true, message: '请选择上级部门' }],
  deptName: [{ required: true, message: '请输入部门名称' }],
  leader: [{ required: true, message: '请输入负责人' }],
}

// Columns
const columns = [
  {
    title: '部门名称',
    dataIndex: 'deptName',
  },
  {
    title: '排序',
    dataIndex: 'sort',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
]

// Table Data
const tableData = ref([])

// 查询
const handleQuery = async () => {
  const res = await getDept(queryForm)
  tableData.value = deepDelChildren(res.data)
}

// 重置查询
const handleResetQuery = () => {
  queryFormRef.value?.resetFields()
  getDeptInfo(queryForm)
}

// 新增
const handleAdd = ({ deptId, status = 2 } = {}) => {
  modalVisible.value = true
  modalTitle.value = '新增部门'

  if (deptId) Object.assign(modalForm, { parentId: deptId, status })
}

// 修改
const handleUpdate = async (record) => {
  modalVisible.value = true
  modalTitle.value = '修改部门信息'

  await nextTick()
  const { parentId, deptName, leader, phone, email, status, deptId } = record
  Object.assign(modalForm, {
    parentId,
    deptName,
    leader,
    phone,
    email,
    status,
    deptId,
  })
}

// 递归删除空Children
function deepDelChildren(data) {
  const depts = data
  const len = depts?.length
  // let len = depts && depts.length;

  for (let i = 0; i < len; i++) {
    if (depts[i].children.length > 0) {
      deepDelChildren(depts[i].children)
    } else {
      delete depts[i].children
    }
  }

  return depts
}

// 取消操作
const handleCancel = () => {
  modalFormRef.value?.resetFields()
}

// Modal 表单提交前检查
const handleBeforeOk = () => {
  modalFormRef.value
    ?.validate()
    .then(async () => {
      if (Reflect.has(modalForm, 'deptId')) {
        const { code, msg } = await updateDept(modalForm, modalForm.deptId)
        if (code === 200) {
          notification.success({
            message: '修改成功',
            description: '部门信息已更新',
          })
        } else {
          notification.error({
            message: '修改失败',
            description: msg,
          })
        }
      } else {
        const { code, msg } = await addDept(modalForm)
        if (code === 200) {
          notification.success({
            message: '新增成功',
            description: '部门已创建',
          })
        } else {
          notification.error({
            message: '新增失败',
            description: msg,
          })
        }
      }
      getDeptInfo()
    })
    .catch(() => {
      message.error('数据校验失败')
    })
}

// 获取部门信息
const getDeptInfo = async (params = {}) => {
  const { data, code, msg } = await getDept(params)
  if (code === 200) {
    tableData.value = deepDelChildren(data)
  } else {
    notification.error({
      message: '获取数据失败',
      description: msg,
    })
  }
}

onMounted(() => {
  getDeptInfo()
})
</script>

<style lang="scss">
.action {
  margin-bottom: 8px;
}
</style>
