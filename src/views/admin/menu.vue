<template>
  <div class="menu-management">
    <div class="page-header">
      <h2>菜单管理</h2>
      <p>管理系统菜单结构和权限配置</p>
    </div>

    <div class="content-card">
      <div class="toolbar">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <PlusOutlined />
            新增菜单
          </a-button>
          <a-button @click="handleExpandAll">
            <ExpandAltOutlined />
            展开全部
          </a-button>
          <a-button @click="handleCollapseAll">
            <ShrinkOutlined />
            收起全部
          </a-button>
        </a-space>
      </div>

      <a-table
        :columns="columns"
        :data-source="menuList"
        :loading="loading"
        :pagination="false"
        :defaultExpandAllRows="true"
        row-key="menuId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'menuType'">
            <a-tag :color="record.menuType === 'M' ? 'blue' : 'green'">
              {{ record.menuType === 'M' ? '目录' : '菜单' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'visible'">
            <a-tag :color="record.visible === 0 ? 'green' : 'red'">
              {{ record.visible === 0 ? '显示' : '隐藏' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleAddChild(record)">
                <PlusOutlined />
                新增子菜单
              </a-button>
              <a-button type="link" size="small" danger @click="handleDelete(record)">
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  PlusOutlined,
  ExpandAltOutlined,
  ShrinkOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'

// 表格列定义
const columns = [
  {
    title: '菜单名称',
    dataIndex: 'title',
    key: 'title',
    width: 200,
  },
  {
    title: '路径',
    dataIndex: 'path',
    key: 'path',
  },
  {
    title: '组件',
    dataIndex: 'component',
    key: 'component',
  },
  {
    title: '类型',
    dataIndex: 'menuType',
    key: 'menuType',
    width: 80,
  },
  {
    title: '权限标识',
    dataIndex: 'permission',
    key: 'permission',
  },
  {
    title: '显示状态',
    dataIndex: 'visible',
    key: 'visible',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
]

// 菜单列表数据
const menuList = ref([
  {
    menuId: 1,
    title: '仪表板',
    path: '/dashboard',
    component: '/dashboard',
    menuType: 'C',
    permission: 'dashboard:view',
    visible: 0,
  },
  {
    menuId: 2,
    title: '系统管理',
    path: '/admin',
    component: '',
    menuType: 'M',
    permission: 'admin:view',
    visible: 0,
    children: [
      {
        menuId: 21,
        title: '用户管理',
        path: '/admin/user',
        component: '/admin/user',
        menuType: 'C',
        permission: 'admin:user:view',
        visible: 0,
      },
      {
        menuId: 22,
        title: '菜单管理',
        path: '/admin/menu',
        component: '/admin/menu',
        menuType: 'C',
        permission: 'admin:menu:view',
        visible: 0,
      },
    ],
  },
])

const loading = ref(false)

// 新增菜单
const handleAdd = () => {
  console.log('新增菜单')
}

// 展开全部
const handleExpandAll = () => {
  console.log('展开全部')
}

// 收起全部
const handleCollapseAll = () => {
  console.log('收起全部')
}

// 编辑菜单
const handleEdit = (record: any) => {
  console.log('编辑菜单:', record)
}

// 新增子菜单
const handleAddChild = (record: any) => {
  console.log('新增子菜单:', record)
}

// 删除菜单
const handleDelete = (record: any) => {
  console.log('删除菜单:', record)
}
</script>

<style lang="scss" scoped>
.menu-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    color: #2d5aa0;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}
</style>
