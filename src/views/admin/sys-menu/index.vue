<template>
  <div class="container">
    <a-card :bordered="false" class="cardStyle" style="margin-bottom: 16px">
      <a-list-item-meta>
        <template #title>
          <div class="akaInfoTitle">菜单管理</div>
        </template>
        <template #description>
          <div class="akaInfoDesc">这里管理菜单和路由，并且绑定菜单api权限</div>
        </template>
        <template #avatar>
          <div style="border-radius: 100px 0 100px 100px; background-color: #eff4f9; padding: 6px">
            <Iconify icon="ic:baseline-menu-open" style="color: black" width="48" height="48" />
          </div>
        </template>
      </a-list-item-meta>
      <a-divider />
      <a-card-meta>
        <template #avatar>
          <a-form :model="queryForm" ref="queryFormRef" layout="inline">
            <a-form-item name="title" label="菜单名称">
              <a-input
                v-model="queryForm.title"
                placeholder="请输入菜单名称"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="visible" label="状态">
              <a-select v-model="queryForm.visible" placeholder="请选择菜单状态">
                <a-option value="1">显示</a-option>
                <a-option value="0">隐藏</a-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleQuery">搜索</a-button>
                <a-button @click="queryFormRef?.resetFields()">重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </template>
      </a-card-meta>
      <template #actions>
        <a-space class="action">
          <a-button type="primary" @click="handleAddMenu()">新增菜单</a-button>
        </a-space>
      </template>
    </a-card>

    <!-- 菜单管理列表 -->
    <a-card :bordered="false" class="cardStyle">
      <a-table :columns="columns" :data-source="tableData" row-key="menuId" :pagination="false">
        <template #icon="{ record }">
          <component :is="record.icon" :style="{ fontSize: '18px' }"></component>
        </template>
        <template #menutype="{ record }">
          <a-tag color="purple" v-if="record.menuType === 'M'">目录</a-tag>
          <a-tag color="orange" v-else-if="record.menuType === 'C'">菜单</a-tag>
          <a-tag color="blue" v-else-if="record.menuType === 'F'">按钮</a-tag>
        </template>
        <template #isFrame="{ record }">
          <a-tag v-if="record.isFrame == '1'" color="green">内部</a-tag>
          <a-tag v-else color="red">外部</a-tag>
        </template>
        <template #visible="{ record }">
          <a-tag v-if="record.visible == '0'" color="green">显示</a-tag>
          <a-tag v-else color="red">隐藏</a-tag>
        </template>
        <template #action="{ record }">
          <a-button v-has="'admin:sysMenu:add'" type="text" @click="handleAddMenu(record.menuId)"
            >新增</a-button
          >
          <a-button v-has="'admin:sysMenu:edit'" type="text" @click="handleUpdate(record)"
            >修改</a-button
          >
          <a-button
            v-has="'admin:sysMenu:remove'"
            type="text"
            @click="
              () => {
                deleteVisible = true
                deleteData = [record.menuId]
              }
            "
            >删除</a-button
          >
        </template>
      </a-table>
    </a-card>

    <!-- 菜单管理新增与提交弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      title-align="start"
      :width="800"
      modal-class="menu-modal"
      @before-ok="handleSubmit"
      @close="
        () => {
          modalFormRef?.resetFields()
          modalForm.menuId = null
        }
      "
    >
      <a-form
        :model="modalForm"
        :rules="modalRules"
        ref="modalFormRef"
        auto-label-width
        label-align="left"
      >
        <a-form-item name="menuType" label="菜单类型">
          <a-radio-group v-model="modalForm.menuType">
            <a-radio value="M">目录</a-radio>
            <a-radio value="C">菜单</a-radio>
            <a-radio value="F">按钮</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item name="parentId" label="上级菜单">
              <a-tree-select
                v-model="modalForm.parentId"
                :treeData="tableData"
                :fieldNames="{ key: 'menuId', icon: '_' }"
                showSearch
                :filterTreeNode="filterTreeNode"
                placeholder="请选择上级菜单"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="title" label="菜单标题">
              <a-input v-model="modalForm.title" placeholder="请输入菜单标题" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item name="icon" label="菜单图标">
              <IconSelect v-model:data="modalForm.icon" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="sort" label="显示排序">
              <a-input-number v-model="modalForm.sort" mode="button" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12" v-if="modalForm.menuType !== 'F'">
            <a-form-item name="menuName" label="路由名称">
              <a-input v-model="modalForm.menuName" placeholder="请输入路由名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="modalForm.menuType !== 'F'">
            <a-form-item name="component" label="组件名称">
              <a-input v-model="modalForm.component" placeholder="请输入组件名称" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12" v-if="modalForm.menuType !== 'F'">
            <a-form-item name="path" label="路由地址">
              <a-input v-model="modalForm.path" placeholder="请输入路由地址" />
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="modalForm.menuType === 'C' || modalForm.menuType === 'F'">
            <a-form-item name="permission" label="权限标识">
              <a-input v-model="modalForm.permission" placeholder="请输入权限标识" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item name="isFrame" label="是否外链" v-if="modalForm.menuType !== 'F'">
          <a-radio-group v-model="modalForm.isFrame">
            <a-radio value="0">是</a-radio>
            <a-radio value="1">否</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="visible" label="菜单状态" v-if="modalForm.menuType !== 'F'">
          <a-radio-group v-model="modalForm.visible">
            <a-radio value="0">显示</a-radio>
            <a-radio value="1">隐藏</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="apis" label="API权限" v-if="modalForm.menuType !== 'M'">
          <a-transfer
            v-model:model-value="modalForm.apis"
            :dataSource="transferData"
            :title="['未授权', '已授权']"
            show-search
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Akiraka 20230210 删除与批量删除 开始 -->
    <DeleteModal
      :data="deleteData"
      :visible="deleteVisible"
      :apiDelete="removeMenu"
      @deleteVisibleChange="() => (deleteVisible = false)"
    />
    <!-- Akiraka 20230210 删除与批量删除 结束 -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, watchEffect, onMounted, nextTick } from 'vue'
import { getMenu } from '@/api/admin/menu'
import { getSysApi } from '@/api/admin/sys-api'
import { addMenu, removeMenu, updateMenu, getMenuDetails } from '@/api/admin/menu'
import { message, notification } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'

// 表单引用
const modalFormRef = ref<FormInstance>()
const queryFormRef = ref<FormInstance>()

// Akiraka 20230210 删除数据
const deleteData = ref([])
// Akiraka 20230210 删除对话框
const deleteVisible = ref(false)
// Akiraka 20230210 监听删除事件
watch(
  () => deleteVisible.value,
  (value) => {
    if (value == false) {
      getSysMenuInfo(pager)
    }
  },
)

const { queryForm, handleQuery } = useQueryData()

// 默认页码
const currentPage = ref(1)
// 表格分页
const pager = {
  count: 0,
  current: 1,
  pageSize: 10,
}

// QueryModel
function useQueryData() {
  // 表单查询
  const queryForm = reactive({})
  const handleQuery = () => {
    getSysMenuInfo(queryForm)
  }

  return {
    queryForm,
    handleQuery,
  }
}

// 默认表单
const modalForm = reactive<any>({
  menuType: 'M',
  sort: 0,
  isFrame: '1',
  visible: '0',
})

// 表单校验
const modalRules = {
  title: [{ required: true, message: '请输入菜单名称' }],
  path: [{ required: true, message: '请输入菜单路由地址' }],
  component: [
    { required: true, message: '请输入菜单路由地址' },
    {
      pattern: /^[\/A-Za-z.-.()*]+$/,
      trigger: 'blur',
      message: '校验规则:  只允许输入字母 a-z 或大写 A-Z 与 -',
    },
  ],
}

// 监听事件 20220715
watchEffect(() => {
  // 当菜单类型为目录时组件地址则为 Layout
  if (modalForm.menuType === 'M') {
    modalForm.component = 'Layout'
  } else {
    // 当菜单类型设置为菜单时 如果为 Layout 则为空
    if (modalForm.component === 'Layout') {
      modalForm.component = ''
      // 当菜单类型设置为菜单时 编辑页面显示正常内容
    }
  }
})

// Table
const columns = [
  { title: '菜单名称', dataIndex: 'title', width: '220' },
  { title: '图标', dataIndex: 'icon', slotName: 'icon', width: '80' },
  { title: '路由地址', dataIndex: 'path', width: '400' },
  { title: '组件地址', dataIndex: 'component', width: '300' },
  { title: '排序', dataIndex: 'sort', width: '80' },
  { title: '类型', dataIndex: 'menuType', slotName: 'menutype', width: '100' },
  { title: '是否外联', dataIndex: 'isFrame', slotName: 'isFrame', width: '100' },
  { title: '显示状态', dataIndex: 'visible', slotName: 'visible', width: '100' },
  { title: '操作', slotName: 'action', width: '220', fixed: 'right' },
]
const tableData = ref([])

// Transfer Data
const transferData = ref([])

// Modal
const modalVisible = ref(false)
const modalTitle = ref('默认标题')

// 创建新菜单
const handleAddMenu = (parentId = null) => {
  modalVisible.value = true
  modalTitle.value = '新增菜单'
  if (parentId) modalForm.parentId = parentId
  getSysMenuInfo()
}

// TreeSearchFilter
const filterTreeNode = (searchVal, nodeData) => {
  return nodeData.title.indexOf(searchVal) > -1
}

// 修改菜单
const handleUpdate = async (record) => {
  const res = await getMenuDetails(record.menuId)
  Object.assign(modalForm, res.data)

  modalTitle.value = '修改菜单'
  modalVisible.value = true
}

// handleSubmit 新增与修改按钮方法 20220713
const handleSubmit = (done) => {
  modalFormRef.value
    ?.validate()
    .then(async () => {
      try {
        if (!modalForm.menuId) {
          console.log('dasdasd', modalForm)
          const response = await addMenu(modalForm)
          if (response.status === 200) {
            message.success('新增成功')
          }
        } else {
          const response = await updateMenu(modalForm, modalForm.menuId)
          if (response.status === 200) {
            message.success('修改成功')
          }
        }
        getSysMenuInfo(pager)
        done()
      } catch (error) {
        message.error('操作失败')
        done(false)
      }
    })
    .catch(() => {
      message.error('表单校验失败')
      done(false)
    })
}

// 获取菜单信息
const getSysMenuInfo = async (params = {}) => {
  try {
    const response = await getMenu(params)
    if (response.status === 200 && response.data) {
      tableData.value = response.data
    }
  } catch (error) {
    notification.error({
      message: '获取菜单信息失败',
      description: '请稍后重试',
    })
  }
}

// 获取API接口信息
const getSysApiInfo = async () => {
  try {
    const response = await getSysApi({ pageSize: 10000, type: 'BUS' })
    if (response.status === 200 && response.data?.list) {
      transferData.value = response.data.list.map((item: any) => {
        return { value: item.id, label: item.title }
      })
    }
  } catch (error) {
    notification.error({
      message: '获取API接口信息失败',
      description: '请稍后重试',
    })
  }
}

onMounted(() => {
  getSysMenuInfo()
  getSysApiInfo()
})
</script>

<style lang="scss">
// 覆盖默认穿梭框样式
.menu-modal {
  .arco-transfer-view {
    height: 350px;
    width: 250px;
  }
}
</style>
