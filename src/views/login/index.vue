<template>
  <div class="account">
    <div class="account-container">
      <div class="account-wrap-login">
        <div class="login-pic">
          <div>
            <img src="/public/login_left_bg.jpg" />
          </div>
        </div>
        <div class="login-form" style="padding: 3rem !important">
          <div class="login-form-container">
            <div class="account-top">
              <div class="account-top-logo">
                <img :src="store.sysConfig?.sys_app_logo" />
                <span class="project-title">用户登录</span>
              </div>
              <div class="account-top-desc">横看成峰侧成岭 远近高低各不同</div>
            </div>
            <!-- 登录表单 -->
            <a-form
              :model="loginForm"
              :rules="loginRules"
              ref="loginFormRef"
              layout="vertical"
              @keyup.enter="handleLogin"
            >
              <a-form-item name="userName">
                <a-input v-model:value="loginForm.userName" placeholder="请输入用户名">
                  <template #prefix>
                    <UserOutlined />
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item name="passWord">
                <a-input-password v-model:value="loginForm.passWord" placeholder="请输入密码">
                  <template #prefix>
                    <LockOutlined />
                  </template>
                </a-input-password>
              </a-form-item>
              <div style="display: flex">
                <div style="width: 65%">
                  <a-form-item name="code">
                    <a-input v-model:value="loginForm.code" placeholder="请输入验证码">
                      <template #prefix>
                        <SafetyOutlined />
                      </template>
                    </a-input>
                  </a-form-item>
                </div>
                <div style="width: 5%"></div>
                <div style="width: 20%">
                  <a-form-item name="code">
                    <img :src="captchUrl" class="captcha" @click="loadCaptcha()" />
                  </a-form-item>
                </div>
              </div>
              <a-space direction="vertical" size="large">
                <a-checkbox>记住密码</a-checkbox>
                <a-button
                  size="large"
                  type="primary"
                  :loading="loading"
                  block
                  @click="originHandleLogin"
                  >登录
                </a-button>
                <div class="login-border">
                  <div class="login-main">
                    <div class="login-title">单点登录</div>
                    <a-button
                      :loading="loading"
                      type="primary"
                      style="width: 100%; padding: 12px 20px; margin-bottom: 30px"
                      @click="handleLogin"
                    >
                      <span v-if="!loading">登 录</span>
                      <span v-else>登 录 中...</span>
                    </a-button>
                  </div>
                </div>
              </a-space>
            </a-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons-vue'
import { login, getCaptcha } from '@/api/admin/login'
import { useUserStore } from '@/store/userInfo'
import type { FormInstance } from 'ant-design-vue'
import queryString from 'query-string'

interface LoginForm {
  userName: string
  passWord: string
  code: string
  uuid?: string
}

const store = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// form
const loginForm = reactive<LoginForm>({
  userName: '',
  passWord: '',
  code: '',
})

// 验证码
const captchUrl = ref<string>('')
// 按钮loading
const loading = ref<boolean>(false)

// rules
const loginRules = {
  userName: [{ required: true, message: '请输入用户名' }],
  passWord: [{ required: true, message: '请输入密码' }],
  code: [{ required: true, message: '请输入验证码' }],
}

// 获取验证码
const loadCaptcha = async (): Promise<void> => {
  const res = await getCaptcha()
  captchUrl.value = res.data
  loginForm.uuid = res.id
}

const route = useRoute()

const handleLogin = () => {
  const redirectUrl = encodeURIComponent(location.origin + route.query.redirect)

  const query = queryString.stringify({
    service: `${import.meta.env.VITE_VUE_APP_BASE_API}/ssologin?redirect_url=${redirectUrl}`,
    serverlogin: `${import.meta.env.VITE_VUE_APP_BASE_API}/ssologin`,
  })

  const apiUrl = `https://sso.xunlei.cn/server/login?${query}`
  location.href = apiUrl
}
onMounted(async () => {
  await loadCaptcha()
})
// 登陆
const originHandleLogin = (): void => {
  loading.value = true
  // 使用表单验证
  loginFormRef.value
    ?.validate()
    .then(async () => {
      try {
        const { code, token, msg } = await login(loginForm)
        if (code == 200) {
          await store.setToken(token)
          message.success({
            content: '登陆成功',
            duration: 2000,
          })
          setTimeout(() => {
            router.push('/admin/sys-api')
            loading.value = false
          }, 500)
        } else {
          message.error(`登陆失败：${msg}`)
        }
      } catch {
        // 登录失败 重新获取验证码
        loadCaptcha()
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 表单验证失败
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
.captcha {
  width: 100px;
  height: 32px;
  cursor: pointer;
}
.account {
  width: 100%;
  margin: 0 auto;
}
.account-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 15px;
  background: #9053c7;
  background: linear-gradient(-135deg, #c850c0, #4158d0);
}
.account-wrap-login {
  width: 960px;
  height: 554px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
}
.account-wrap-login .login-pic {
  background-color: #0259e6 !important;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 50%;
}
.account-wrap-login .login-pic img {
  max-width: 100%;
}
.account-wrap-login .login-form {
  width: 50%;
  display: flex;
  flex-direction: column;
  background: #fff;
}
.account-wrap-login .login-form-container {
  margin: auto;
  width: 100%;
}
.account-wrap-login .login-form-title {
  padding-bottom: 15px;
  text-align: center;
}
@media (max-width: 991px) {
  .account-wrap-login .login-pic {
    display: none;
  }
  .account-wrap-login .login-form {
    width: 100%;
    margin: auto;
  }
}
.account-wrap-login .account-top {
  text-align: center;
}
.account-wrap-login .account-top-logo {
  text-align: center;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.account-wrap-login .account-top-logo img {
  width: 45px;
}
.account-wrap-login .account-top-logo .project-title {
  background: linear-gradient(92.06deg, #33c2ff -17.9%, #1e6fff 43.39%, #1e6fff 99.4%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 24px;
  line-height: 1.25;
  font-weight: 500;
  margin-left: 10px;
}
.account-wrap-login .account-top-desc {
  font-size: 14px;
  color: #808695;
  margin-bottom: 20px;
}
@media (max-width: 640px) {
  .account-wrap-login {
    width: 100%;
    padding: 30px;
    height: auto;
  }
}
</style>
