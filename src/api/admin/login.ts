import request from '../../utils/request'

interface LoginData {
  username: string
  password: string
  captcha?: string
  [key: string]: unknown
}

export function login(data: LoginData) {
  return request({
    url: '/api/v1/login',
    method: 'post',
    data,
  })
}

export function getCaptcha() {
  return request({
    url: '/api/v1/captcha',
    method: 'get',
  })
}

export function getAppConfig() {
  return request({
    url: '/api/v1/app-config',
    method: 'get',
  })
}

// 根据角色获取菜单
export function getUserMenuRole() {
  return request({
    url: '/mock/api/v1/menurole',
    method: 'get',
  })
}
