<template>
  <div class="navbar">
    <div class="left-side" @click="onCollapse">
      <MenuFoldOutlined v-if="!props.collapsed" />
      <MenuUnfoldOutlined v-else />
    </div>
    <ul class="right-side">
      <li>
        <Avatar />
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts" name="NavbarComponent">
import Avatar from './Avatar/index.vue'
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue'

interface Props {
  collapsed: boolean
}

interface Emits {
  onCollapse: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onCollapse = (): void => {
  emit('onCollapse')
}
</script>

<style lang="scss" scoped>
.navbar {
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: #2d5aa0;
  border-bottom: 1px solid #1c3f7a;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  .left-side {
    height: 50px;
    width: 50px;
    font-size: 18px;
    line-height: 50px;
    text-align: center;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
    color: #fff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .right-side {
    list-style: none;
    display: flex;
    padding-right: 30px;

    & > li {
      display: flex;
      align-items: center;
      padding: 10px;
    }

    :deep(.ant-btn) {
      background-color: transparent;
      border-color: rgba(255, 255, 255, 0.3);
      color: #fff;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
</style>
