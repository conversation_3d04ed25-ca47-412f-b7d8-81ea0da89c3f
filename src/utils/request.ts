import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStore } from '../store/userInfo'
import router from '../router'
import Cookies from 'js-cookie'

// create an axios instance
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL,
  timeout: 8000,
})

// request interceptor
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 设置请求头部 Authorization
    if (config.headers) {
      // 如果在营销系统登陆则用模拟等有效token，后续去掉
      const token = Cookies.get(import.meta.env.VITE_VUE_APP_TOKEN_KEY)
        ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhc2NvcGUiOiIiLCJleHAiOjQ5MDc2NzcxMDcsImlkZW50aXR5IjoxLCJuaWNlIjoiYWRtaW4iLCJvcmlnX2lhdCI6MTc1NDA0MTEwNywicm9sZWlkIjoxLCJyb2xla2V5IjoiYWRtaW4iLCJyb2xlbmFtZSI6Iuezu-e7n-euoeeQhuWRmCJ9.EczcSc72PAx12AR6jYLA97u1uGNqEriKeOYTwY2SSCU'
        : ''
      config.headers['Authorization'] = 'Bearer ' + token
      config.headers['Content-Type'] = 'application/json'
    }
    return config
  },
  (error: unknown) => {
    console.error(error)
    return Promise.reject(error)
  },
)

// response interceptor
service.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error: unknown) => {
    const store = useUserStore()
    const { code, msg } =
      (error as { response?: { data?: { code?: number; msg?: string } } }).response?.data || {}
    // 如果过期则退出登录
    if (code === 401) {
      message.error({
        content: 'Token 已过期, 请重新登陆',
        duration: 3000,
      })
      // 重定向路由到登陆页面
      store.userLogout()
      // Akiraka 20230410 重定向到登录页面
      return router.push('/login')
    } else {
      message.error({
        content: error.message,
        duration: 3000,
      })
      return Promise.reject(msg)
    }
  },
)

export default service
